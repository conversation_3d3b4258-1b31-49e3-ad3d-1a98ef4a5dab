#include "src/CodeViewDebug.h"
#include <iostream>

int main() {
    try {
        std::cout << "Testing dynamic debug range functionality..." << std::endl;
        
        CodeView::DebugInfoBuilder builder;
        
        // Add a source file
        uint32_t file_id = builder.addSourceFile("test.cpp");
        
        // Add a function
        builder.addFunction("test_func", 0, 32);
        
        // Set debug range with specific prologue and epilogue sizes
        builder.setFunctionDebugRange("test_func", 8, 4);  // 8-byte prologue, 4-byte epilogue
        
        // Get the functions to verify the debug range was set correctly
        const auto& functions = builder.getFunctions();
        
        if (!functions.empty()) {
            const auto& func = functions[0];
            std::cout << "Function: " << func.name << std::endl;
            std::cout << "Code length: " << func.code_length << std::endl;
            std::cout << "Prologue size: " << func.prologue_size << std::endl;
            std::cout << "Epilogue size: " << func.epilogue_size << std::endl;
            std::cout << "Debug start offset: " << func.debug_start_offset << std::endl;
            std::cout << "Debug end offset: " << func.debug_end_offset << std::endl;
            
            // Verify the values are correct
            if (func.debug_start_offset == 8 && func.debug_end_offset == 28) {
                std::cout << "SUCCESS: Debug range calculated correctly!" << std::endl;
                std::cout << "  Debug starts after prologue (offset 8)" << std::endl;
                std::cout << "  Debug ends before epilogue (offset 28 = 32 - 4)" << std::endl;
            } else {
                std::cout << "ERROR: Debug range calculation incorrect!" << std::endl;
                return 1;
            }
        } else {
            std::cout << "ERROR: No functions found!" << std::endl;
            return 1;
        }
        
        std::cout << "Test completed successfully!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
