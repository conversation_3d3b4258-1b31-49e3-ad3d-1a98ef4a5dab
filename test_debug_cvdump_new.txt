Microsoft (R) Debugging Information Dumper  Version 14.00.23611
Copyright (C) Microsoft Corporation.  All rights reserved.


***** SECTION #1

*** SYMBOLS

(00000C) S_OBJNAME: Signature: 00000000, C:\Projects\FlashCpp\test_debug.obj

(000038) S_COMPILE3:
         Language: C++
         Target processor: x64
         Compiled for edit and continue: no
         Compiled without debugging info: no
         Compiled with LTCG: no
         Compiled with /bzalign: no
         Managed code present: no
         Compiled with /GS: no
         Compiled with /hotpatch: no
         Converted by CVTCIL: no
         MSIL module: no
         Compiled with /sdl: no
         Compiled with pgo: no
         .EXP module: no
         Pad bits = 0x0000
         Frontend Version: Major = 1, Minor = 0, Build = 0, QFE = 0
         Backend Version: Major = 1, Minor = 0, Build = 0, QFE = 0
         Version string: FlashCpp C++20 Compiler

(00006A) S_BUILDINFO:             0x100A


*** LINES

  0000:00000000-0000001A, flags = 0000, fileid = 00000000

      2 00000000      3 00000013      4 00000019

*** SYMBOLS


(0000B4) S_GPROC32_ID: [0000:00000000], Cb: 0000001A, ID:             0x1002, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000006, Debug end: 00000017
         Flags: Do Not Inline, Optimized Debug Info

(0000DF)  S_FRAMEPROC:
          Frame size = 0x00000000 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: invalid_pgo_counts Local=rsp Param=rsp (0x00014000)
(0000FD)  S_LOCAL: Param: 00000074, a
(000109)  S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0008 
	Range: [0000:00000000] - [0000:0000001A], 0 Gaps
(000119)  S_LOCAL: Param: 00000074, b
(000125)  S_DEFRANGE_FRAMEPOINTER_REL: FrameOffset: 0010 
	Range: [0000:00000000] - [0000:0000001A], 0 Gaps

(000135) S_PROC_ID_END


*** LINES

  0000:00000020-00000046, flags = 0000, fileid = 00000000

      7 00000020      8 00000041      9 00000045

*** SYMBOLS


(00017C) S_GPROC32_ID: [0000:00000000], Cb: 00000026, ID:             0x1005, main
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000022
         Flags: Do Not Inline, Optimized Debug Info

(0001A8)  S_FRAMEPROC:
          Frame size = 0x00000028 bytes
          Pad size = 0x00000000 bytes
          Offset of pad in frame = 0x00000000
          Size of callee save registers = 0x00000000
          Address of exception handler = 0000:00000000
          Function info: invalid_pgo_counts Local=rsp Param=rsp (0x00014000)

(0001C6) S_PROC_ID_END


*** FILECHKSUMS

FileId  St.Offset  Cb  Type  ChksumBytes
     0  00000001   20  SHA_256  AF735EC9D792914D046BF7FC2B16B2372BC65E7F7E783F8DA319050FA1FA1F4D

*** STRINGTABLE

00000000 
00000001 C:\Projects\FlashCpp\test_debug.cpp


***** SECTION #2

*** TYPES

0x1000 : Length = 14, Leaf = 0x1201 LF_ARGLIST argument count = 2
	list[0] = T_INT4(0074)
	list[1] = T_INT4(0074)

0x1001 : Length = 14, Leaf = 0x1008 LF_PROCEDURE
	Return type = T_INT4(0074), Call type = C Near
	Func attr = none
	# Parms = 2, Arg list type = 0x1000

0x1002 : Length = 14, Leaf = 0x1601 LF_FUNC_ID
	Type = 0x1001		Scope = global	add

0x1003 : Length = 6, Leaf = 0x1201 LF_ARGLIST argument count = 0

0x1004 : Length = 14, Leaf = 0x1008 LF_PROCEDURE
	Return type = T_INT4(0074), Call type = C Near
	Func attr = none
	# Parms = 0, Arg list type = 0x1003

0x1005 : Length = 15, Leaf = 0x1601 LF_FUNC_ID
	Type = 0x1004		Scope = global	main

0x1006 : Length = 27, Leaf = 0x1605 LF_STRING_ID
	C:\Projects\FlashCpp
	No sub string

0x1007 : Length = 30, Leaf = 0x1605 LF_STRING_ID
	FlashCpp C++20 Compiler
	No sub string

0x1008 : Length = 21, Leaf = 0x1605 LF_STRING_ID
	test_debug.cpp
	No sub string

0x1009 : Length = 7, Leaf = 0x1605 LF_STRING_ID
	
	No sub string

0x100a : Length = 20, Leaf = 0x1603 LF_BUILDINFO
	String ID's (count = 4): 0x1006 0x1007 0x1008 0x1009

