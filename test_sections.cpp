#include "src/ObjFileWriter.h"
#include <iostream>

int main() {
    try {
        std::cout << "Testing ObjectFileWriter with new sections..." << std::endl;
        
        ObjectFileWriter writer;
        
        // Add some test data to the text section
        std::vector<char> test_code = {0x48, 0x89, 0xe5, 0xc3}; // Simple function: mov rbp, rsp; ret
        writer.add_data(test_code, SectionType::TEXT);
        
        // Add a test function symbol
        writer.add_function_symbol("test_func", 0);
        
        // Add some test data to the data section
        std::vector<char> test_data = {0x01, 0x02, 0x03, 0x04};
        writer.add_data(test_data, SectionType::DATA);
        
        // Add exception info
        writer.add_function_exception_info("test_func", 0, 4);
        
        // Finalize debug info
        writer.finalize_debug_info();
        
        // Write the object file
        writer.write("test_sections.obj");
        
        std::cout << "Object file created successfully with all sections!" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
